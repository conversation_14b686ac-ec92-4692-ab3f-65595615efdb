<div>
    {{-- View Permission-Role Assignment Component --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">Permission-Role Assignment Details</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Assignment ID</label>
                        <p class="form-control-plaintext">{{ $permissionRoleId ?? 'N/A' }}</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Assigned At</label>
                        <p class="form-control-plaintext">Assignment date will be displayed here</p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Role</label>
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">Role name will be displayed here</h6>
                                <p class="card-text text-muted">Role description will be displayed here</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Permission</label>
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">Permission name will be displayed here</h6>
                                <p class="card-text text-muted">Permission description will be displayed here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end">
                <button type="button" class="btn btn-secondary me-2">Back</button>
                <button type="button" class="btn btn-primary">Edit Assignment</button>
            </div>
        </div>
    </div>
</div>
