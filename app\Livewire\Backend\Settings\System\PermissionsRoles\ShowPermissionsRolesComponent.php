<?php

namespace App\Livewire\Backend\Settings\System\PermissionsRoles;

use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;
use App\Traits\Defaults\HasSortables;

class ShowPermissionsRolesComponent extends Component
{
    use WithPagination;
    use HasSortables;

    #[Url]
    public $permissionsRolesSearch = '';
    #[Url]
    public $permissionsRolesPaginate = 5;
    #[Url]
    public $filterByRole = '';
    #[Url]
    public $filterByPermission = '';

    public function doSort(string $column)
    {
        $this->sortingDirection($column);
    }

    #[On(['permissionRoleCreated', 'permissionRoleUpdated', 'permissionRoleDeleted'])]
    public function render()
    {
        return view('livewire.backend.settings.system.permissions-roles.show-permissions-roles-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
