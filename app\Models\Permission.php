<?php

namespace App\Models;

use App\Builders\PermissionBuilder;
use App\Casts\TransformStringCast;
use App\Enums\Permissions\PermissionClusterEnum;
use App\Traits\Defaults\HasFilterables;
use App\Traits\Defaults\HasSearchables;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Permission extends Model
{
    /** @use HasFactory<\Database\Factories\PermissionFactory> */
    use HasFactory;
    use HasSearchables;
    use HasFilterables;

    protected $fillable = [
        'name',
        'slug',
        'cluster',
        'description',
    ];

    protected $searchable = [
        'name',
        'cluster',
        'description',
    ];

    protected $filterable = [
        'filterByCluster' => 'cluster',
    ];

    protected $casts = [
        'id' => 'integer',
        'name' => TransformStringCast::class . ':capitalize',
        'slug' => 'string',
        'cluster' => PermissionClusterEnum::class,
        'description' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function newEloquentBuilder($query): Builder
    {
        return new PermissionBuilder($query);
    }

    /**
     * The roles that belong to the permission.
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'permission_roles');
    }
}
