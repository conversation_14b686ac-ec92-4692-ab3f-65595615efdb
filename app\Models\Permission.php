<?php

namespace App\Models;

use App\Casts\TransformStringCast;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Permission extends Model
{
    /** @use HasFactory<\Database\Factories\PermissionFactory> */
    use HasFactory;
    use HasSearchables;
    use HasFilterables;

    protected $fillable = [
        'name',
        'slug',
        'cluster',
        'description',
    ];

    protected $searchable = [
        'name',
        'cluster',
        'description',
    ];

    protected $casts = [
        'id' => 'integer',
        'name' => TransformStringCast::class . ':capitalize',
        'slug' => 'string',
        'cluster' => 'string',
        'description' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
