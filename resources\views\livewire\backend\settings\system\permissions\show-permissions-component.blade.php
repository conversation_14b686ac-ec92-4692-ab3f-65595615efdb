@php
    $tablePagination = $this->permissions->isNotEmpty() && $this->totalPermissions > 5 ? true : false;
    $permissionsAvailable = $tablePagination;
    $rowNumber = 0;
@endphp
<div>
    <x-search :results="$permissionsAvailable" searchProperty="permissionsSearch"
    searchPlaceholder="Search by permission names...." formWidth="max-w-full" />


    <div class="max-h-[400px] overflow-y-auto custom-scrollbar pe-3 ps-0 pt-0 shadow-sm">
        @foreach ($this->permissions as $permission)
            <div wire:key="{{ 'permission-' . $permission->id }}" class="duration-300 ease-in-out rounded-none sm:pb-0  sm:bg-transparent">
                <div class="bg-slate-800 mb-0 rounded">
                    <dt
                        class="flex  items-center mt-1 md:mx-1 mb-0 text-sm text-slate-300 md:text-base">
                        <!-- Buttons -->
                        <div class=" flex items-center md:space-x-1 ">
                            {{-- <x-action-buttons :id="Crypt::encrypt($permission->id)" editAction="openModalToUpdatePermission"
                                deleteAction="openModalToDeletePermission" /> --}}

                            <!-- Permission Name -->
                            <div class="line-clamp-1 ms-1">
                                {{ ucfirst($permission->name) . ' permission' }}
                            </div>
                        </div>
                    </dt>
                </div>
            </div>
        @endforeach
    </div>

    {{-- Pagination --}}
    {{-- <x-paginator :pages="$this->pages" :totalRecords="$this->totalPermissions" selectClass="text-slate-500" paginateRecords="paginatePermissions"
        :results="$permissionsAvailable" :paginationEnabled="$tablePagination" /> --}}


</div>
