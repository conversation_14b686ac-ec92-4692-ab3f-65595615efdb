<div>
    {{-- Create Permission-Role Assignment Component --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">Assign Permission to Role</h5>
        </div>
        <div class="card-body">
            <form wire:submit="createPermissionRole">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="selectedRole" class="form-label">Select Role</label>
                            <select wire:model="selectedRole" class="form-select" id="selectedRole">
                                <option value="">Choose a role...</option>
                                {{-- Role options will go here --}}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="selectedPermission" class="form-label">Select Permission</label>
                            <select wire:model="selectedPermission" class="form-select" id="selectedPermission">
                                <option value="">Choose a permission...</option>
                                {{-- Permission options will go here --}}
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        This will grant the selected permission to the selected role.
                    </div>
                </div>

                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-secondary me-2">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign Permission</button>
                </div>
            </form>
        </div>
    </div>
</div>
