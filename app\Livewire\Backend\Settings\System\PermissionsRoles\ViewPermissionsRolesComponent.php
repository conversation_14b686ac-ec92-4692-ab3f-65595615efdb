<?php

namespace App\Livewire\Backend\Settings\System\PermissionsRoles;

use Livewire\Component;
use Livewire\Attributes\Locked;

class ViewPermissionsRolesComponent extends Component
{
    #[Locked]
    public $permissionRoleId;

    public function mount($permissionRoleId)
    {
        $this->permissionRoleId = $permissionRoleId;
    }

    public function render()
    {
        return view('livewire.backend.settings.system.permissions-roles.view-permissions-roles-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
