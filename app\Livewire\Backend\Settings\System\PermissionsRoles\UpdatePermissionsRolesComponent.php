<?php

namespace App\Livewire\Backend\Settings\System\PermissionsRoles;

use Livewire\Component;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Computed;

class UpdatePermissionsRolesComponent extends Component
{
    #[Locked]
    public $permissionRoleId;
    
    public $selectedRole = '';
    public $selectedPermission = '';

    public function mount($permissionRoleId)
    {
        $this->permissionRoleId = $permissionRoleId;
    }

    public function updatePermissionRole()
    {
        // TODO: Implement permission-role update logic
    }

    public function render()
    {
        return view('livewire.backend.settings.system.permissions-roles.update-permissions-roles-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
