<?php

namespace App\Enums\Permissions;

enum PermissionClusterEnum: 
{
    case USERS = 'users';
    case ROLES = 'roles';
    case PERMISSIONS = 'permissions';
    case SYSTEM_SETTINGS = 'system_settings';
    case SECURITIES = 'securities';
    case CONTENTS = 'contents';
    case REPORTINGS = 'reportings';
    case AUDITS = 'audits';
    case ORGANISATIONS = 'organisations';
    case PLATFORMS = 'platforms';

    public function label(): string
    {
        return match ($this) {
            self::USERS => 'Users',
            self::ROLES => 'Roles',
            self::PERMISSIONS => 'Permissions',
            self::SYSTEM_SETTINGS => 'System Settings',
            self::SECURITIES => 'Securities',
            self::CONTENTS => 'Contents',
            self::REPORTINGS => 'Reportings',
            self::AUDITS => 'Audits',
            self::ORGANISATIONS => 'Organisations',
            self::PLATFORMS => 'Platforms',
        };
    }

    public static function getClusterOptions(): array
    {
        $options = [];

        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }

        return $options;
    }

    public function description(): string
    {
        return match ($this) {
            self::USERS => 'Permissions related to creating, updating, deleting, and managing user accounts',
            self::ROLES => 'Permissions for creating, modifying, and assigning roles within the system',
            self::PERMISSIONS => 'Permissions for managing and configuring system permissions',
            self::SYSTEM_SETTINGS => 'Permissions for configuring system-wide settings and preferences',
            self::SECURITY => 'Permissions related to security features, IP management, and access controls',
            self::CONTENTS => 'Permissions for creating, editing, and managing content within the platform',
            self::REPORTING => 'Permissions for generating, viewing, and managing reports and analytics',
            self::AUDIT => 'Permissions for viewing audit logs and system activity tracking',
            self::ORGANISATION => 'Permissions specific to organisation-level management and settings',
            self::PLATFORM => 'Permissions for platform-wide administration and configuration',
        };
    }
}
