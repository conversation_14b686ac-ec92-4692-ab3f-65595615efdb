<?php

namespace App\Livewire\Backend\Settings\System\PermissionsRoles;

use Livewire\Component;
use Livewire\Attributes\Locked;

class DeletePermissionsRolesComponent extends Component
{
    #[Locked]
    public $permissionRoleId;

    public function mount($permissionRoleId)
    {
        $this->permissionRoleId = $permissionRoleId;
    }

    public function deletePermissionRole()
    {
        // TODO: Implement permission-role deletion logic
    }

    public function render()
    {
        return view('livewire.backend.settings.system.permissions-roles.delete-permissions-roles-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
