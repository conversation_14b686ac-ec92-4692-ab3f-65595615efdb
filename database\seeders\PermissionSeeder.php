<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Enums\Permissions\PermissionClusterEnum;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // User Management
            [
                'name' => 'View Users',
                'slug' => 'users.view',
                'cluster' => PermissionClusterEnum::USER_MANAGEMENT->value,
                'description' => 'View user accounts and their details',
            ],
            [
                'name' => 'Create Users',
                'slug' => 'users.create',
                'cluster' => PermissionClusterEnum::USER_MANAGEMENT->value,
                'description' => 'Create new user accounts',
            ],
            [
                'name' => 'Edit Users',
                'slug' => 'users.edit',
                'cluster' => PermissionClusterEnum::USER_MANAGEMENT->value,
                'description' => 'Edit existing user accounts',
            ],
            [
                'name' => 'Delete Users',
                'slug' => 'users.delete',
                'cluster' => PermissionClusterEnum::USER_MANAGEMENT->value,
                'description' => 'Delete user accounts',
            ],

            // Role Management
            [
                'name' => 'View Roles',
                'slug' => 'roles.view',
                'cluster' => PermissionClusterEnum::ROLE_MANAGEMENT->value,
                'description' => 'View roles and their details',
            ],
            [
                'name' => 'Create Roles',
                'slug' => 'roles.create',
                'cluster' => PermissionClusterEnum::ROLE_MANAGEMENT->value,
                'description' => 'Create new roles',
            ],
            [
                'name' => 'Edit Roles',
                'slug' => 'roles.edit',
                'cluster' => PermissionClusterEnum::ROLE_MANAGEMENT->value,
                'description' => 'Edit existing roles',
            ],
            [
                'name' => 'Delete Roles',
                'slug' => 'roles.delete',
                'cluster' => PermissionClusterEnum::ROLE_MANAGEMENT->value,
                'description' => 'Delete roles',
            ],

            // Permission Management
            [
                'name' => 'View Permissions',
                'slug' => 'permissions.view',
                'cluster' => PermissionClusterEnum::PERMISSION_MANAGEMENT->value,
                'description' => 'View permissions and their details',
            ],
            [
                'name' => 'Manage Permissions',
                'slug' => 'permissions.manage',
                'cluster' => PermissionClusterEnum::PERMISSION_MANAGEMENT->value,
                'description' => 'Assign and revoke permissions',
            ],

            // System Settings
            [
                'name' => 'View System Settings',
                'slug' => 'settings.view',
                'cluster' => PermissionClusterEnum::SYSTEM_SETTINGS->value,
                'description' => 'View system configuration settings',
            ],
            [
                'name' => 'Manage System Settings',
                'slug' => 'settings.manage',
                'cluster' => PermissionClusterEnum::SYSTEM_SETTINGS->value,
                'description' => 'Modify system configuration settings',
            ],

            // Security
            [
                'name' => 'View Security Settings',
                'slug' => 'security.view',
                'cluster' => PermissionClusterEnum::SECURITY->value,
                'description' => 'View security settings and configurations',
            ],
            [
                'name' => 'Manage IP Restrictions',
                'slug' => 'security.ip-management',
                'cluster' => PermissionClusterEnum::SECURITY->value,
                'description' => 'Manage allowed and blocked IP addresses',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::create(array_merge($permission, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
