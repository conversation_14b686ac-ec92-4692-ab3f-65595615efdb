<?php

namespace App\Livewire\Backend\Settings\System\PermissionsRoles;

use Livewire\Component;
use Livewire\Attributes\Computed;

class CreatePermissionsRolesComponent extends Component
{
    public $selectedRole = '';
    public $selectedPermission = '';

    public function createPermissionRole()
    {
        // TODO: Implement permission-role assignment logic
    }

    public function render()
    {
        return view('livewire.backend.settings.system.permissions-roles.create-permissions-roles-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
